import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Phone, Mail, MessageCircle, User } from "lucide-react";
import { motion } from "framer-motion";

export default function RepresentativeCard({ representative, index }) {
  const handleCall = (phone) => {
    window.open(`tel:${phone}`, '_self');
  };

  const handleEmail = (email) => {
    window.open(`mailto:${email}`, '_self');
  };

  const handleWhatsApp = (phone) => {
    window.open(`https://wa.me/${phone.replace(/\D/g, '')}`, '_blank');
  };

  const getInitials = (name) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getPositionColor = (position) => {
    switch (position) {
      case "Department Representative":
        return "bg-purple-100 text-purple-800 border-purple-200";
      case "Assistant Course Representative":
        return "bg-orange-100 text-orange-800 border-orange-200";
      default:
        return "bg-blue-100 text-blue-800 border-blue-200";
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
    >
      <Card className="group hover:shadow-xl transition-all duration-300 border-slate-200 bg-white/80 backdrop-blur-sm hover:bg-white hover:scale-[1.02]">
        <CardContent className="p-6">
          <div className="flex items-start gap-4">
            <div className="relative">
              {representative.profile_image ? (
                <img
                  src={representative.profile_image}
                  alt={representative.full_name}
                  className="w-16 h-16 rounded-xl object-cover border-2 border-slate-200"
                />
              ) : (
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center border-2 border-slate-200">
                  <span className="text-white font-semibold text-lg">
                    {getInitials(representative.full_name)}
                  </span>
                </div>
              )}
              <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-white"></div>
            </div>

            <div className="flex-1 min-w-0">
              <h3 className="text-xl font-bold text-slate-900 mb-1">
                {representative.full_name}
              </h3>
              <p className="text-slate-600 font-medium mb-2">
                {representative.course_program}
              </p>
              
              <div className="flex flex-wrap gap-2 mb-3">
                <Badge variant="outline" className={getPositionColor(representative.position)}>
                  {representative.position}
                </Badge>
                <Badge variant="outline" className="bg-slate-50 text-slate-700">
                  {representative.year_level}
                </Badge>
              </div>

              <p className="text-sm text-slate-500 mb-3">
                {representative.department}
              </p>

              {representative.about && (
                <p className="text-sm text-slate-600 mb-4 line-clamp-2">
                  {representative.about}
                </p>
              )}

              <div className="flex flex-wrap gap-2">
                {representative.phone_number && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleCall(representative.phone_number)}
                    className="hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300"
                  >
                    <Phone className="w-4 h-4 mr-1" />
                    Call
                  </Button>
                )}
                
                {representative.email && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEmail(representative.email)}
                    className="hover:bg-green-50 hover:text-green-700 hover:border-green-300"
                  >
                    <Mail className="w-4 h-4 mr-1" />
                    Email
                  </Button>
                )}
                
                {representative.whatsapp_number && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleWhatsApp(representative.whatsapp_number)}
                    className="hover:bg-emerald-50 hover:text-emerald-700 hover:border-emerald-300"
                  >
                    <MessageCircle className="w-4 h-4 mr-1" />
                    WhatsApp
                  </Button>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}