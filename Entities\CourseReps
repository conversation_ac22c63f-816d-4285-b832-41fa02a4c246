{"name": "CourseRep", "type": "object", "properties": {"full_name": {"type": "string", "description": "Full name of the course representative"}, "course_program": {"type": "string", "description": "Course program (e.g., Computer Science, Civil Engineering, etc.)"}, "department": {"type": "string", "enum": ["School of Engineering", "School of Sciences", "School of Petroleum Studies", "School of Geosciences", "School of Management", "School of Environmental Studies"], "description": "Academic department"}, "year_level": {"type": "string", "enum": ["Level 100", "Level 200", "Level 300", "Level 400"], "description": "Academic year level"}, "phone_number": {"type": "string", "description": "Phone number"}, "email": {"type": "string", "format": "email", "description": "Email address"}, "whatsapp_number": {"type": "string", "description": "WhatsApp number"}, "profile_image": {"type": "string", "description": "Profile image URL"}, "position": {"type": "string", "enum": ["Course Representative", "Assistant Course Representative", "Department Representative"], "default": "Course Representative", "description": "Position held"}, "about": {"type": "string", "description": "Brief description about the representative"}}, "required": ["full_name", "course_program", "department", "year_level", "phone_number"]}