import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Users, BookOpen, Building, Award } from "lucide-react";

export default function StatsOverview({ representatives }) {
  const totalReps = representatives.length;
  const departments = [...new Set(representatives.map(rep => rep.department))].length;
  const courses = [...new Set(representatives.map(rep => rep.course_program))].length;
  const levels = [...new Set(representatives.map(rep => rep.year_level))].length;

  const stats = [
    {
      title: "Total Representatives",
      value: totalReps,
      icon: Users,
      color: "bg-blue-500",
    },
    {
      title: "Departments",
      value: departments,
      icon: Building,
      color: "bg-green-500",
    },
    {
      title: "Course Programs",
      value: courses,
      icon: BookOpen,
      color: "bg-purple-500",
    },
    {
      title: "Academic Levels",
      value: levels,
      icon: Award,
      color: "bg-orange-500",
    },
  ];

  return (
    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
      {stats.map((stat) => (
        <Card key={stat.title} className="border-slate-200 bg-white/80 backdrop-blur-sm">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className={`p-2 rounded-lg ${stat.color} bg-opacity-10`}>
                <stat.icon className={`w-5 h-5 ${stat.color.replace('bg-', 'text-')}`} />
              </div>
              <CardTitle className="text-2xl font-bold text-slate-900">
                {stat.value}
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <p className="text-sm text-slate-600">{stat.title}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}