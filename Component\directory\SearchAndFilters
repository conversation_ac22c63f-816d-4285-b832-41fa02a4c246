import React from "react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Search, Filter, X } from "lucide-react";
import { Badge } from "@/components/ui/badge";

export default function SearchAndFilters({ 
  searchTerm, 
  setSearchTerm, 
  selectedDepartment, 
  setSelectedDepartment,
  selectedYear,
  setSelectedYear,
  selectedPosition,
  setSelectedPosition,
  clearFilters,
  hasActiveFilters 
}) {
  const departments = [
    "School of Engineering",
    "School of Sciences", 
    "School of Petroleum Studies",
    "School of Geosciences",
    "School of Management",
    "School of Environmental Studies"
  ];

  const years = ["Level 100", "Level 200", "Level 300", "Level 400"];
  const positions = ["Course Representative", "Assistant Course Representative", "Department Representative"];

  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-slate-200 shadow-sm">
      <div className="flex flex-col lg:flex-row gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400" />
          <Input
            placeholder="Search by name or course program..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 h-12 border-slate-200 focus:border-blue-400 focus:ring-blue-400"
          />
        </div>
        
        <div className="flex flex-wrap gap-3">
          <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
            <SelectTrigger className="w-48 h-12 border-slate-200">
              <SelectValue placeholder="Department" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Departments</SelectItem>
              {departments.map((dept) => (
                <SelectItem key={dept} value={dept}>{dept}</SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={selectedYear} onValueChange={setSelectedYear}>
            <SelectTrigger className="w-32 h-12 border-slate-200">
              <SelectValue placeholder="Year" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Years</SelectItem>
              {years.map((year) => (
                <SelectItem key={year} value={year}>{year}</SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={selectedPosition} onValueChange={setSelectedPosition}>
            <SelectTrigger className="w-48 h-12 border-slate-200">
              <SelectValue placeholder="Position" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Positions</SelectItem>
              {positions.map((position) => (
                <SelectItem key={position} value={position}>{position}</SelectItem>
              ))}
            </SelectContent>
          </Select>

          {hasActiveFilters && (
            <Button
              variant="outline"
              onClick={clearFilters}
              className="h-12 px-4 border-slate-200 hover:bg-slate-50"
            >
              <X className="w-4 h-4 mr-2" />
              Clear
            </Button>
          )}
        </div>
      </div>

      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2 mt-4">
          {searchTerm && (
            <Badge variant="secondary" className="bg-blue-50 text-blue-700">
              Search: {searchTerm}
            </Badge>
          )}
          {selectedDepartment && selectedDepartment !== "all" && (
            <Badge variant="secondary" className="bg-blue-50 text-blue-700">
              {selectedDepartment}
            </Badge>
          )}
          {selectedYear && selectedYear !== "all" && (
            <Badge variant="secondary" className="bg-blue-50 text-blue-700">
              {selectedYear}
            </Badge>
          )}
          {selectedPosition && selectedPosition !== "all" && (
            <Badge variant="secondary" className="bg-blue-50 text-blue-700">
              {selectedPosition}
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}