// JavaScript Example: Reading Entities
// Filterable fields: full_name, course_program, department, year_level, phone_number, email, whatsapp_number, profile_image, position, about
async function fetchCourseRepEntities() {
    const response = await fetch(`https://app.base44.com/api/apps/686ac3a264276bb3e616dd05/entities/CourseRep`, {
        headers: {
            'api_key': '87752693f05a4b7bbceacaaa4390396d', // or use await User.me() to get the API key
            'Content-Type': 'application/json'
        }
    });
    const data = await response.json();
    console.log(data);
}

// JavaScript Example: Updating an Entity
// Filterable fields: full_name, course_program, department, year_level, phone_number, email, whatsapp_number, profile_image, position, about
async function updateCourseRepEntity(entityId, updateData) {
    const response = await fetch(`https://app.base44.com/api/apps/686ac3a264276bb3e616dd05/entities/CourseRep/${entityId}`, {
        method: 'PUT',
        headers: {
            'api_key': '87752693f05a4b7bbceacaaa4390396d', // or use await User.me() to get the API key
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
    });
    const data = await response.json();
    console.log(data);
}