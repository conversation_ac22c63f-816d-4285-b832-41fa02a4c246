import React, { useState, useEffect } from "react";
import { CourseRep } from "@/entities/CourseRep";
import { motion, AnimatePresence } from "framer-motion";
import { Loader2, UserX } from "lucide-react";

import SearchAndFilters from "../components/directory/SearchAndFilters";
import RepresentativeCard from "../components/directory/RepresentativeCard";
import StatsOverview from "../components/directory/StatsOverview";

export default function Directory() {
  const [representatives, setRepresentatives] = useState([]);
  const [filteredReps, setFilteredReps] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedDepartment, setSelectedDepartment] = useState("all");
  const [selectedYear, setSelectedYear] = useState("all");
  const [selectedPosition, setSelectedPosition] = useState("all");

  useEffect(() => {
    loadRepresentatives();
  }, []);

  useEffect(() => {
    filterRepresentatives();
  }, [representatives, searchTerm, selectedDepartment, selectedYear, selectedPosition]);

  const loadRepresentatives = async () => {
    try {
      const data = await CourseRep.list("-created_date");
      setRepresentatives(data);
    } catch (error) {
      console.error("Error loading representatives:", error);
    } finally {
      setLoading(false);
    }
  };

  const filterRepresentatives = () => {
    let filtered = representatives;

    if (searchTerm) {
      filtered = filtered.filter(rep =>
        rep.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        rep.course_program.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (selectedDepartment && selectedDepartment !== "all") {
      filtered = filtered.filter(rep => rep.department === selectedDepartment);
    }

    if (selectedYear && selectedYear !== "all") {
      filtered = filtered.filter(rep => rep.year_level === selectedYear);
    }

    if (selectedPosition && selectedPosition !== "all") {
      filtered = filtered.filter(rep => rep.position === selectedPosition);
    }

    setFilteredReps(filtered);
  };

  const clearFilters = () => {
    setSearchTerm("");
    setSelectedDepartment("all");
    setSelectedYear("all");
    setSelectedPosition("all");
  };

  const hasActiveFilters = searchTerm || 
    (selectedDepartment && selectedDepartment !== "all") ||
    (selectedYear && selectedYear !== "all") ||
    (selectedPosition && selectedPosition !== "all");

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center min-h-[60vh]">
            <div className="text-center">
              <Loader2 className="w-12 h-12 animate-spin text-blue-600 mx-auto mb-4" />
              <p className="text-xl font-semibold text-slate-700">Loading representatives...</p>
              <p className="text-slate-500 mt-2">Please wait while we fetch the directory</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-8"
          >
            <h1 className="text-4xl md:text-5xl font-bold text-slate-900 mb-4">
              Course Representatives
            </h1>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              Connect with your course representatives at the University of Mines and Technology
            </p>
          </motion.div>

          <StatsOverview representatives={representatives} />
        </div>

        <SearchAndFilters
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          selectedDepartment={selectedDepartment}
          setSelectedDepartment={setSelectedDepartment}
          selectedYear={selectedYear}
          setSelectedYear={setSelectedYear}
          selectedPosition={selectedPosition}
          setSelectedPosition={setSelectedPosition}
          clearFilters={clearFilters}
          hasActiveFilters={hasActiveFilters}
        />

        <div className="mt-8">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-slate-900">
              {filteredReps.length} Representative{filteredReps.length !== 1 ? 's' : ''}
              {hasActiveFilters && (
                <span className="text-lg font-normal text-slate-500 ml-2">
                  (filtered from {representatives.length})
                </span>
              )}
            </h2>
          </div>

          <AnimatePresence mode="wait">
            {filteredReps.length > 0 ? (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
              >
                {filteredReps.map((rep, index) => (
                  <RepresentativeCard key={rep.id} representative={rep} index={index} />
                ))}
              </motion.div>
            ) : (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="text-center py-16"
              >
                <UserX className="w-16 h-16 text-slate-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-slate-700 mb-2">
                  No representatives found
                </h3>
                <p className="text-slate-500 mb-4">
                  {hasActiveFilters 
                    ? "Try adjusting your search or filters" 
                    : "No course representatives have been added yet"}
                </p>
                {hasActiveFilters && (
                  <button
                    onClick={clearFilters}
                    className="text-blue-600 hover:text-blue-800 font-medium"
                  >
                    Clear all filters
                  </button>
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
}