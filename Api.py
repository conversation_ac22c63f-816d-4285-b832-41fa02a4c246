# Python Example: Reading Entities
# Filterable fields: full_name, course_program, department, year_level, phone_number, email, whatsapp_number, profile_image, position, about
import requests

def make_api_request(api_path, method='GET', data=None):
    url = f'https://app.base44.com/api/{api_path}'
    headers = {
        'api_key': '87752693f05a4b7bbceacaaa4390396d',
        'Content-Type': 'application/json'
    }
    if method.upper() == 'GET':
        response = requests.request(method, url, headers=headers, params=data)
    else:
        response = requests.request(method, url, headers=headers, json=data)
    response.raise_for_status()
    return response.json()

entities = make_api_request(f'apps/686ac3a264276bb3e616dd05/entities/CourseRep')
print(entities)

# Python Example: Updating an Entity
# Filterable fields: full_name, course_program, department, year_level, phone_number, email, whatsapp_number, profile_image, position, about
def update_entity(entity_id, update_data):
    response = requests.put(
        f'https://app.base44.com/api/apps/686ac3a264276bb3e616dd05/entities/CourseRep/{entity_id}',
        headers={
            'api_key': '87752693f05a4b7bbceacaaa4390396d',
            'Content-Type': 'application/json'
        },
        json=update_data
    )
    response.raise_for_status()
    return response.json()